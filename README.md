# OLS Regression Analysis for Real Estate Data

This project provides a Ju<PERSON>ter notebook for running Ordinary Least Squares (OLS) regression on real estate market data using conda for environment management.

## Features

- **Automated data extraction** from JSON real estate data
- **OLS regression analysis** using statsmodels
- **Standardized output format** matching API specifications
- **Data visualization** and model diagnostics
- **Conda environment management** for reproducible results

## Quick Start

### Prerequisites
- Anaconda or Miniconda installed
- Sample real estate data in JSON format

### Setup

1. **Create and activate the conda environment:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

2. **Launch Jupyter Lab:**
   ```bash
   chmod +x run_notebook.sh
   ./run_notebook.sh
   ```

### Manual Setup (Alternative)

```bash
# Create environment
conda env create -f environment.yml

# Activate environment
conda activate ols-regression

# Launch Jupyter Lab
jupyter lab ols_regression.ipynb
```

## Data Format

### Input Data Structure
The notebook expects JSON data with the following structure:

```json
{
  "subjectProperty": { ... },
  "marketComps": [
    {
      "comp_property_area": 1421,
      "comp_property_bedrooms": "3",
      "comp_property_bathsFull": "2",
      "comp_property_bathsHalf": "1",
      "comp_property_yearBuilt": "2000",
      "comp_close_price": "340000",
      ...
    }
  ]
}
```

### Output Format
The regression results are returned in this format:

```json
{
  "olsDebug": {
    "nObs": 16,
    "coeffs": {
      "area": 122,
      "beds": 18500,
      "baths": 10800,
      "yearBuilt": 950
    },
    "tStats": {
      "area": 7.1,
      "beds": 3.4,
      "baths": 2.8,
      "yearBuilt": 1.9
    },
    "r2": 0.82
  }
}
```

## Regression Model

The OLS regression model predicts property prices using four key features:

- **Area**: Property square footage
- **Beds**: Number of bedrooms
- **Baths**: Total bathrooms (full + 0.5 × half baths)
- **Year Built**: Construction year

**Model equation:**
```
Price = β₀ + β₁×Area + β₂×Beds + β₃×Baths + β₄×YearBuilt + ε
```

## Files Description

- `ols_regression.ipynb` - Main Jupyter notebook with regression analysis
- `environment.yml` - Conda environment specification
- `setup.sh` - Environment setup script
- `run_notebook.sh` - Script to launch Jupyter Lab
- `sample_data.json` - Sample real estate data
- `README.md` - This documentation

## Dependencies

- Python 3.11
- pandas - Data manipulation
- numpy - Numerical computing
- statsmodels - Statistical modeling
- scipy - Scientific computing
- matplotlib - Plotting
- seaborn - Statistical visualization
- jupyter - Notebook environment

## Usage Notes

1. **Data Quality**: The notebook automatically filters out records with missing values
2. **Minimum Observations**: Requires at least 5 valid comparable sales for regression
3. **Output Rounding**: Coefficients are rounded to integers, t-stats to 1 decimal, R² to 2 decimals
4. **Diagnostics**: Includes model diagnostics and visualization for validation

## Troubleshooting

### Environment Issues
```bash
# Remove and recreate environment
conda env remove -n ols-regression
./setup.sh
```

### Jupyter Kernel Issues
```bash
# Reinstall kernel
conda activate ols-regression
python -m ipykernel install --user --name ols-regression --display-name "OLS Regression (Python 3.11)"
```

### Data Issues
- Ensure `sample_data.json` is in the same directory as the notebook
- Check that market comps have required fields: area, bedrooms, bathrooms, year built, close price
- Verify numeric fields can be converted to appropriate data types

## Example Output

Running the notebook with the provided sample data should produce results similar to:

```json
{
  "olsDebug": {
    "nObs": 30,
    "coeffs": {
      "area": 125,
      "beds": 15000,
      "baths": 8500,
      "yearBuilt": 800
    },
    "tStats": {
      "area": 6.8,
      "beds": 2.9,
      "baths": 2.1,
      "yearBuilt": 1.7
    },
    "r2": 0.78
  }
}
```

The results are also saved to `ols_results.json` for programmatic access.
