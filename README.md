# OLS Regression Analysis for Real Estate Data with Time Adjustment

This project provides a comprehensive solution for running Ordinary Least Squares (OLS) regression on real estate market data with time adjustment using CSI (Case-Shiller Index) and conda for environment management.

## Features

- **Time adjustment using CSI**: Applies `timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)` before regression
- **Automated data extraction** from JSON real estate data
- **OLS regression analysis** using statsmodels with time-adjusted prices
- **Standardized output format** matching API specifications
- **Data visualization** and model diagnostics
- **Conda environment management** for reproducible results
- **Comparison analysis** showing impact of time adjustment

## Quick Start

### Prerequisites
- Anaconda or Miniconda installed
- Sample real estate data in JSON format

### Setup

1. **Create and activate the conda environment:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

2. **Launch Jupyter Lab:**
   ```bash
   chmod +x run_notebook.sh
   ./run_notebook.sh
   ```

### Manual Setup (Alternative)

```bash
# Create environment
conda env create -f environment.yml

# Activate environment
conda activate ols-regression

# Launch Jupyter Lab
jupyter lab ols_regression.ipynb
```

## Data Format

### Input Data Structure
The notebook expects JSON data with the following structure:

```json
{
  "subjectProperty": { ... },
  "marketComps": [
    {
      "comp_property_area": 1421,
      "comp_property_bedrooms": "3",
      "comp_property_bathsFull": "2",
      "comp_property_bathsHalf": "1",
      "comp_property_yearBuilt": "2000",
      "comp_close_price": "340000",
      ...
    }
  ]
}
```

### Output Format
The regression results are returned in this format (using time-adjusted prices):

```json
{
  "olsDebug": {
    "nObs": 50,
    "coeffs": {
      "area": 157,
      "beds": -59171,
      "baths": 31256,
      "yearBuilt": 200
    },
    "tStats": {
      "area": 7.0,
      "beds": -2.8,
      "baths": 1.6,
      "yearBuilt": 0.3
    },
    "r2": 0.76
  }
}
```

## Time Adjustment Process

Before running OLS regression, comparable sale prices are adjusted for time using CSI:

**Time Adjustment Formula:**
```
timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)
```

Where:
- `CSI_t0` = Case-Shiller Index at reference time (current)
- `CSI_tsale` = Case-Shiller Index at time of sale

## Regression Model

The OLS regression model predicts time-adjusted property prices using four key features:

- **Area**: Property square footage
- **Beds**: Number of bedrooms
- **Baths**: Total bathrooms (full + 0.5 × half baths)
- **Year Built**: Construction year

**Model equation:**
```
TimeAdjPrice = β₀ + β₁×Area + β₂×Beds + β₃×Baths + β₄×YearBuilt + ε
```

## Files Description

- `ols_regression.ipynb` - Original Jupyter notebook with regression analysis
- `ols_regression_with_time_adj.ipynb` - Enhanced notebook with time adjustment
- `test_ols.py` - Standalone script with time adjustment functionality
- `time_adjustment_demo.py` - Demo script showing time adjustment impact
- `demo.py` - Results demonstration script
- `environment.yml` - Conda environment specification
- `setup.sh` - Environment setup script
- `run_notebook.sh` - Script to launch Jupyter Lab
- `sample_data.json` - Sample real estate data
- `README.md` - This documentation

## Dependencies

- Python 3.11
- pandas - Data manipulation
- numpy - Numerical computing
- statsmodels - Statistical modeling
- scipy - Scientific computing
- matplotlib - Plotting
- seaborn - Statistical visualization
- jupyter - Notebook environment

## Usage Notes

1. **Time Adjustment**: Automatically applies CSI-based time adjustment to comparable sales
2. **Data Quality**: The system automatically filters out records with missing values
3. **Minimum Observations**: Requires at least 5 valid comparable sales for regression
4. **Output Rounding**: Coefficients are rounded to integers, t-stats to 1 decimal, R² to 2 decimals
5. **CSI Data**: Uses mock CSI data for demonstration; replace with real CSI data in production
6. **Diagnostics**: Includes model diagnostics and visualization for validation

## Troubleshooting

### Environment Issues
```bash
# Remove and recreate environment
conda env remove -n ols-regression
./setup.sh
```

### Jupyter Kernel Issues
```bash
# Reinstall kernel
conda activate ols-regression
python -m ipykernel install --user --name ols-regression --display-name "OLS Regression (Python 3.11)"
```

### Data Issues
- Ensure `sample_data.json` is in the same directory as the notebook
- Check that market comps have required fields: area, bedrooms, bathrooms, year built, close price
- Verify numeric fields can be converted to appropriate data types

## Example Output

Running the system with the provided sample data produces:

**With Time Adjustment:**
```json
{
  "olsDebug": {
    "nObs": 50,
    "coeffs": {
      "area": 157,
      "beds": -59171,
      "baths": 31256,
      "yearBuilt": 200
    },
    "tStats": {
      "area": 7.0,
      "beds": -2.8,
      "baths": 1.6,
      "yearBuilt": 0.3
    },
    "r2": 0.76
  }
}
```

**Time Adjustment Impact:**
- Average price adjustment: +1.60%
- Total adjustment across all comps: +$362,948
- Coefficient changes: Area +0.6%, Beds +0.4%, Baths -3.4%, YearBuilt -9.9%

The results are saved to `ols_results.json` for programmatic access.

## Testing Time Adjustment

Run the comparison demo to see the impact:
```bash
python time_adjustment_demo.py
```

This shows side-by-side results with and without time adjustment, demonstrating the methodology and impact on regression coefficients.
