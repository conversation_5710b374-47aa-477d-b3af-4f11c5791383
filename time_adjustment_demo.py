#!/usr/bin/env python3
"""
Demo script showing the impact of time adjustment on OLS regression results
"""

import json
import pandas as pd
import numpy as np
import statsmodels.api as sm
from datetime import datetime

# Import our functions
from test_ols import extract_comp_features, run_ols_regression, get_csi_values, apply_time_adjustment

def compare_with_without_time_adjustment():
    """Compare OLS results with and without time adjustment"""
    
    print("=" * 80)
    print("TIME ADJUSTMENT IMPACT ANALYSIS")
    print("=" * 80)
    
    # Load data
    with open('sample_data.json', 'r') as f:
        data = json.load(f)
    
    print(f"📊 Loaded {len(data['marketComps'])} market comparables")
    
    # Extract data WITHOUT time adjustment
    print("\n🔍 ANALYSIS WITHOUT TIME ADJUSTMENT:")
    print("-" * 50)
    df_no_adj = extract_comp_features(data['marketComps'], apply_time_adj=False)
    print(f"✓ Extracted {len(df_no_adj)} valid comparables")
    
    if len(df_no_adj) >= 5:
        ols_no_adj, model_no_adj = run_ols_regression(df_no_adj)
        print("Results without time adjustment:")
        print(json.dumps({"olsDebug": ols_no_adj}, indent=2))
    
    # Extract data WITH time adjustment
    print("\n🕒 ANALYSIS WITH TIME ADJUSTMENT:")
    print("-" * 50)
    df_with_adj = extract_comp_features(data['marketComps'], apply_time_adj=True)
    print(f"✓ Extracted {len(df_with_adj)} valid comparables")
    
    # Show time adjustment details
    if len(df_with_adj) > 0:
        total_adjustment = df_with_adj['price'].sum() - df_with_adj['original_price'].sum()
        avg_adjustment_pct = ((df_with_adj['price'] / df_with_adj['original_price'] - 1) * 100).mean()
        print(f"✓ Average time adjustment: {avg_adjustment_pct:.2f}%")
        print(f"✓ Total price adjustment: ${total_adjustment:,.0f}")
        
        # Show CSI data used
        print("\n📈 CSI Data Used:")
        csi_data = get_csi_values()
        for month, csi in sorted(csi_data.items()):
            print(f"  {month}: {csi}")
    
    if len(df_with_adj) >= 5:
        ols_with_adj, model_with_adj = run_ols_regression(df_with_adj)
        print("\nResults with time adjustment:")
        print(json.dumps({"olsDebug": ols_with_adj}, indent=2))
    
    # Compare results
    if len(df_no_adj) >= 5 and len(df_with_adj) >= 5:
        print("\n📊 COMPARISON ANALYSIS:")
        print("-" * 50)
        
        print("Coefficient Changes:")
        for var in ['area', 'beds', 'baths', 'yearBuilt']:
            old_coeff = ols_no_adj['coeffs'][var]
            new_coeff = ols_with_adj['coeffs'][var]
            change = new_coeff - old_coeff
            change_pct = (change / old_coeff * 100) if old_coeff != 0 else 0
            print(f"  {var:10}: {old_coeff:8,} → {new_coeff:8,} (Δ {change:+6,}, {change_pct:+5.1f}%)")
        
        print("\nT-Statistic Changes:")
        for var in ['area', 'beds', 'baths', 'yearBuilt']:
            old_tstat = ols_no_adj['tStats'][var]
            new_tstat = ols_with_adj['tStats'][var]
            change = new_tstat - old_tstat
            print(f"  {var:10}: {old_tstat:6.1f} → {new_tstat:6.1f} (Δ {change:+5.1f})")
        
        print(f"\nR-squared Change:")
        old_r2 = ols_no_adj['r2']
        new_r2 = ols_with_adj['r2']
        r2_change = new_r2 - old_r2
        print(f"  R²: {old_r2:.3f} → {new_r2:.3f} (Δ {r2_change:+.3f})")
    
    # Show sample time adjustments
    print("\n📅 SAMPLE TIME ADJUSTMENTS:")
    print("-" * 50)
    
    # Show a few examples of time adjustment
    sample_data = df_with_adj.head(10)
    for _, row in sample_data.iterrows():
        if pd.notna(row['close_date']):
            original = row['original_price']
            adjusted = row['price']
            adjustment_factor = adjusted / original
            date_str = row['close_date'].split()[0] if row['close_date'] else 'Unknown'
            print(f"  {date_str}: ${original:7,.0f} → ${adjusted:7,.0f} (×{adjustment_factor:.4f})")
    
    print("\n✅ FINAL OUTPUT (with time adjustment):")
    print("=" * 50)
    print(json.dumps({"olsDebug": ols_with_adj}, indent=2))

def show_csi_methodology():
    """Explain the CSI methodology used"""
    
    print("\n" + "=" * 80)
    print("CSI TIME ADJUSTMENT METHODOLOGY")
    print("=" * 80)
    
    print("📋 FORMULA:")
    print("  timeAdjPrice = comp_close_price × (CSI_t0 / CSI_tsale)")
    print("  where:")
    print("    - CSI_t0 = Case-Shiller Index at reference time (current)")
    print("    - CSI_tsale = Case-Shiller Index at time of sale")
    
    print("\n📈 CSI VALUES USED (Mock Data for Charlotte/Huntersville):")
    csi_data = get_csi_values()
    for month, csi in sorted(csi_data.items()):
        if month == '2025-06':
            print(f"  {month}: {csi} ← Reference point (t0)")
        else:
            print(f"  {month}: {csi}")
    
    print("\n🔍 EXAMPLE CALCULATION:")
    test_price = 400000
    test_date = "2025-02-28 00:00:00"
    adjusted_price = apply_time_adjustment(test_price, test_date)
    
    sale_month = "2025-02"
    ref_month = "2025-06"
    csi_sale = csi_data[sale_month]
    csi_ref = csi_data[ref_month]
    
    print(f"  Original Price: ${test_price:,}")
    print(f"  Sale Date: {test_date.split()[0]}")
    print(f"  CSI at sale ({sale_month}): {csi_sale}")
    print(f"  CSI at reference ({ref_month}): {csi_ref}")
    print(f"  Adjustment Factor: {csi_ref} / {csi_sale} = {csi_ref/csi_sale:.4f}")
    print(f"  Adjusted Price: ${test_price:,} × {csi_ref/csi_sale:.4f} = ${adjusted_price:,.0f}")
    
    print("\n💡 INTERPRETATION:")
    print("  - Prices from earlier months are adjusted upward to current market levels")
    print("  - This accounts for general market appreciation over time")
    print("  - Results in more accurate regression coefficients")
    print("  - Reduces bias from temporal price trends")

if __name__ == "__main__":
    compare_with_without_time_adjustment()
    show_csi_methodology()
