#!/usr/bin/env python3
"""
Test script for OLS regression functionality
"""

import json
import pandas as pd
import numpy as np
import statsmodels.api as sm

def extract_comp_features(market_comps):
    """Extract features from market comparables data"""
    comp_data = []
    
    for comp in market_comps:
        try:
            # Extract features
            area = comp.get('comp_property_area')
            bedrooms = comp.get('comp_property_bedrooms')
            baths_full = comp.get('comp_property_bathsFull')
            baths_half = comp.get('comp_property_bathsHalf')
            year_built = comp.get('comp_property_yearBuilt')
            close_price = comp.get('comp_close_price')
            
            # Convert to numeric and handle missing values
            if all(v is not None for v in [area, bedrooms, baths_full, baths_half, year_built, close_price]):
                area = float(area)
                bedrooms = int(bedrooms)
                baths_full = int(baths_full)
                baths_half = int(baths_half)
                year_built = int(year_built)
                close_price = float(close_price)
                
                # Calculate total bathrooms
                total_baths = baths_full + (baths_half * 0.5)
                
                comp_data.append({
                    'area': area,
                    'beds': bedrooms,
                    'baths': total_baths,
                    'yearBuilt': year_built,
                    'price': close_price,
                    'address': comp.get('comp_property_address', 'Unknown')
                })
        except (ValueError, TypeError) as e:
            print(f"Skipping comp due to data issue: {e}")
            continue
    
    return pd.DataFrame(comp_data)

def run_ols_regression(df):
    """Run OLS regression and return results in the specified format"""
    # Prepare the data
    X = df[['area', 'beds', 'baths', 'yearBuilt']].copy()
    y = df['price'].copy()
    
    # Add constant term for intercept
    X = sm.add_constant(X)
    
    # Fit the model
    model = sm.OLS(y, X).fit()
    
    # Extract results
    n_obs = int(model.nobs)
    
    # Get coefficients (excluding intercept)
    coeffs = {
        'area': round(model.params['area']),
        'beds': round(model.params['beds']),
        'baths': round(model.params['baths']),
        'yearBuilt': round(model.params['yearBuilt'])
    }
    
    # Get t-statistics (excluding intercept)
    t_stats = {
        'area': round(model.tvalues['area'], 1),
        'beds': round(model.tvalues['beds'], 1),
        'baths': round(model.tvalues['baths'], 1),
        'yearBuilt': round(model.tvalues['yearBuilt'], 1)
    }
    
    # Get R-squared
    r2 = round(model.rsquared, 2)
    
    # Create output in specified format
    ols_debug = {
        'nObs': n_obs,
        'coeffs': coeffs,
        'tStats': t_stats,
        'r2': r2
    }
    
    return ols_debug, model

def main():
    print("Testing OLS Regression...")
    
    # Load the JSON data
    try:
        with open('sample_data.json', 'r') as f:
            data = json.load(f)
        print(f"✓ Loaded data with {len(data['marketComps'])} market comps")
    except FileNotFoundError:
        print("✗ sample_data.json not found")
        return
    except json.JSONDecodeError:
        print("✗ Invalid JSON in sample_data.json")
        return
    
    # Extract comparable sales data
    df_comps = extract_comp_features(data['marketComps'])
    print(f"✓ Extracted {len(df_comps)} valid comparable sales")
    
    if len(df_comps) < 5:
        print(f"✗ Insufficient data for regression. Need at least 5 observations, have {len(df_comps)}")
        return
    
    # Run the regression
    try:
        ols_results, model = run_ols_regression(df_comps)
        print("✓ OLS regression completed successfully")
        
        print("\nResults:")
        print(json.dumps({"olsDebug": ols_results}, indent=2))
        
        # Save results
        output_data = {
            'olsDebug': ols_results,
            'dataInfo': {
                'totalComps': len(data['marketComps']),
                'validComps': len(df_comps),
                'dataSource': 'sample_data.json'
            }
        }
        
        with open('ols_results.json', 'w') as f:
            json.dump(output_data, f, indent=2)
        print("✓ Results saved to 'ols_results.json'")
        
    except Exception as e:
        print(f"✗ Error running regression: {e}")
        return

if __name__ == "__main__":
    main()
